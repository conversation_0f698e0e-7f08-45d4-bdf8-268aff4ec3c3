import { NextRequest, NextResponse } from 'next/server';
import { updateTool, deleteTool } from '@/lib/supabase';
import { validateApiKey } from '@/lib/auth';

/**
 * PUT /api/admin/tools/[id]
 * Update a specific tool (admin only)
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = params;

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Tool ID is required' },
        { status: 400 }
      );
    }

    const updates = await request.json();
    
    // Transform camelCase to snake_case for database fields if needed
    const dbUpdates: any = {};
    
    // Handle common field transformations
    if (updates.contentStatus !== undefined) {
      dbUpdates.content_status = updates.contentStatus;
    }
    if (updates.logoUrl !== undefined) {
      dbUpdates.logo_url = updates.logoUrl;
    }
    if (updates.shortDescription !== undefined) {
      dbUpdates.short_description = updates.shortDescription;
    }
    if (updates.detailedDescription !== undefined) {
      dbUpdates.detailed_description = updates.detailedDescription;
    }
    if (updates.isVerified !== undefined) {
      dbUpdates.is_verified = updates.isVerified;
    }
    if (updates.isClaimed !== undefined) {
      dbUpdates.is_claimed = updates.isClaimed;
    }
    if (updates.aiGenerationStatus !== undefined) {
      dbUpdates.ai_generation_status = updates.aiGenerationStatus;
    }
    if (updates.lastScrapedAt !== undefined) {
      dbUpdates.last_scraped_at = updates.lastScrapedAt;
    }
    if (updates.submissionType !== undefined) {
      dbUpdates.submission_type = updates.submissionType;
    }
    if (updates.submissionSource !== undefined) {
      dbUpdates.submission_source = updates.submissionSource;
    }
    if (updates.contentQualityScore !== undefined) {
      dbUpdates.content_quality_score = updates.contentQualityScore;
    }
    if (updates.lastAiUpdate !== undefined) {
      dbUpdates.last_ai_update = updates.lastAiUpdate;
    }

    // Copy other fields directly
    Object.keys(updates).forEach(key => {
      if (!dbUpdates.hasOwnProperty(key) && !key.includes('camelCase')) {
        dbUpdates[key] = updates[key];
      }
    });

    const updatedTool = await updateTool(id, dbUpdates);

    return NextResponse.json({
      success: true,
      data: updatedTool,
    });
  } catch (error) {
    console.error('Error updating tool:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update tool' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/admin/tools/[id]
 * Delete a specific tool (admin only)
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = params;

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Tool ID is required' },
        { status: 400 }
      );
    }

    await deleteTool(id);

    return NextResponse.json({
      success: true,
      message: 'Tool deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting tool:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete tool' },
      { status: 500 }
    );
  }
}
